interface StatusIndicatorProps {
  index: number;
  status: "bear" | "accumulation" | "altseason-start" | "full-altseason";
}

export default function StatusIndicator({
  index,
  status,
}: StatusIndicatorProps) {
  // Controllo di sicurezza rigoroso per valori undefined
  if (typeof index !== "number" || isNaN(index)) {
    return <div>Loading...</div>;
  }

  const safeIndex = index;
  const getSegmentColor = (segmentIndex: number) => {
    const segments = [
      { min: 0, max: 25, color: "bg-red-500" },
      { min: 25, max: 50, color: "bg-yellow-500" },
      { min: 50, max: 75, color: "bg-green-400" },
      { min: 75, max: 100, color: "bg-green-500" },
    ];

    const segment = segments[segmentIndex];
    const isActive = safeIndex >= segment.min;

    return isActive ? segment.color : "bg-gray-200 dark:bg-gray-700";
  };

  const getSegmentLabel = (segmentIndex: number) => {
    const labels = [
      "Bear Market",
      "Accumulazione",
      "Altseason",
      "Full Altseason",
    ];
    return labels[segmentIndex];
  };

  const getSegmentRange = (segmentIndex: number) => {
    const ranges = ["<25%", "25-50%", "50-75%", ">75%"];
    return ranges[segmentIndex];
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Barra principale */}
      <div className="relative mb-6">
        <div className="flex h-4 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
          {[0, 1, 2, 3].map((segmentIndex) => (
            <div
              key={segmentIndex}
              className={`flex-1 transition-all duration-500 ${getSegmentColor(
                segmentIndex
              )}`}
            />
          ))}
        </div>

        {/* Indicatore posizione corrente */}
        <div
          className="absolute top-0 h-4 w-1 bg-white border-2 border-gray-800 dark:border-white rounded-full transform -translate-x-1/2 transition-all duration-500"
          style={{ left: `${Math.min(Math.max(safeIndex, 0), 100)}%` }}
        />
      </div>

      {/* Etichette */}
      <div className="grid grid-cols-4 gap-2 text-center">
        {[0, 1, 2, 3].map((segmentIndex) => (
          <div key={segmentIndex} className="flex flex-col">
            <div
              className={`text-xs font-medium mb-1 ${
                status ===
                ["bear", "accumulation", "altseason-start", "full-altseason"][
                  segmentIndex
                ]
                  ? "text-gray-900 dark:text-white"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            >
              {getSegmentLabel(segmentIndex)}
            </div>
            <div className="text-xs text-gray-400 dark:text-gray-500">
              {getSegmentRange(segmentIndex)}
            </div>
          </div>
        ))}
      </div>

      {/* Statistiche aggiuntive */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="text-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Indice Attuale
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {safeIndex.toFixed(1)}%
          </div>
        </div>
      </div>
    </div>
  );
}
