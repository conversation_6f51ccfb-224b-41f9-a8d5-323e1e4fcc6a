export interface CryptoData {
  id: number;
  name: string;
  symbol: string;
  slug: string;
  cmc_rank: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  date_added: string;
  tags: string[];
  platform: {
    id: number;
    name: string;
    symbol: string;
    slug: string;
    token_address: string;
  } | null;
  self_reported_circulating_supply: number;
  self_reported_market_cap: number;
  quote: {
    USD: {
      price: number;
      volume_24h: number;
      volume_change_24h: number;
      percent_change_1h: number;
      percent_change_24h: number;
      percent_change_7d: number;
      percent_change_30d: number;
      percent_change_60d: number;
      percent_change_90d: number;
      market_cap: number;
      market_cap_dominance: number;
      fully_diluted_market_cap: number;
      last_updated: string;
    };
  };
}

export interface CMCResponse {
  status: {
    timestamp: string;
    error_code: number;
    error_message: string | null;
    elapsed: number;
    credit_count: number;
    notice: string | null;
  };
  data: CryptoData[];
}

export interface ProcessedCrypto {
  id: number;
  name: string;
  symbol: string;
  slug: string;
  rank: number;
  price: number;
  marketCap: number;
  change90d: number;
  outperformsBTC: boolean;
  logo?: string;
  isBitcoin?: boolean;
}

export interface AltSeasonResult {
  index: number; // Percentage (0-100)
  totalAltcoins: number;
  outperformingCount: number;
  btcChange90d: number;
  cryptos: ProcessedCrypto[];
  lastUpdated: string;
  status: "bear" | "accumulation" | "altseason-start" | "full-altseason";
}

export interface CacheData {
  data: AltSeasonResult;
  timestamp: number;
  expiresAt: number;
}

// Lista di stablecoin e token backed da escludere
export const EXCLUDED_SYMBOLS = [
  // Stablecoin
  "USDT",
  "USDC",
  "DAI",
  "BUSD",
  "TUSD",
  "FRAX",
  "USDD",
  "GUSD",
  "USDP",
  "LUSD",
  "FDUSD",
  "PYUSD",
  "CRVUSD",
  "FXUSD",
  "USD1",
  "USDS",
  "USDY",
  "USDB",
  "USDR",

  // Asset-backed tokens
  "WBTC",
  "STETH",
  "WSTETH",
  "RETH",
  "CBETH",
  "SFRXETH",
  "LSETH",
  "SWETH",
  "OSETH",
  "EZETH",
  "RSETH",
  "PUFETH",
  "METH",
  "EETH",
  "ANKRETH",
  "SWELL",

  // Altri token wrapped o derivati
  "WETH",
  "RENBTC",
  "SBTC",
  "TBTC",
  "HBTC",
  "BTCB",
  "CLINK",
  "AAVE",

  // Token di exchange che potrebbero essere considerati non "altcoin pure"
  "LEO",
  "CRO",
  "OKB",
  "HT",
  "KCS",
  "GT",
  "MX",
];

export const CACHE_DURATION = 15 * 60 * 1000; // 15 minuti in millisecondi
