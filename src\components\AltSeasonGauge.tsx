"use client";

import { useEffect, useState } from "react";

interface AltSeasonGaugeProps {
  index: number;
  status: "bear" | "accumulation" | "altseason-start" | "full-altseason";
}

export default function AltSeasonGauge({ index, status }: AltSeasonGaugeProps) {
  const [animatedIndex, setAnimatedIndex] = useState(0);

  // Controllo di sicurezza rigoroso per valori undefined
  if (typeof index !== "number" || isNaN(index)) {
    return <div>Loading...</div>;
  }

  const safeIndex = index;

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedIndex(safeIndex);
    }, 500);

    return () => clearTimeout(timer);
  }, [safeIndex]);

  const getColor = () => {
    if (safeIndex < 25) return "text-red-500";
    if (safeIndex < 50) return "text-yellow-500";
    if (safeIndex < 75) return "text-green-400";
    return "text-green-500";
  };

  const getStrokeColor = () => {
    if (safeIndex < 25) return "#ef4444"; // red-500
    if (safeIndex < 50) return "#eab308"; // yellow-500
    if (safeIndex < 75) return "#4ade80"; // green-400
    return "#22c55e"; // green-500
  };

  const circumference = 2 * Math.PI * 90; // raggio 90
  const strokeDasharray = circumference;
  const strokeDashoffset =
    circumference - (animatedIndex / 100) * circumference;

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <div className="relative w-64 h-64 mb-6">
        {/* Cerchio di sfondo */}
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 200 200"
        >
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-gray-200 dark:text-gray-700"
          />

          {/* Cerchio di progresso */}
          <circle
            cx="100"
            cy="100"
            r="90"
            stroke={getStrokeColor()}
            strokeWidth="8"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-1000 ease-out"
          />
        </svg>

        {/* Testo centrale */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className={`text-4xl md:text-5xl font-bold ${getColor()}`}>
            {animatedIndex.toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            AltSeason Index
          </div>
        </div>
      </div>

      {/* Indicatore di stato */}
      <div className="text-center">
        <div className={`text-lg font-semibold ${getColor()}`}>
          {getStatusLabel(status)}
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {getStatusDescription(status)}
        </div>
      </div>
    </div>
  );
}

function getStatusLabel(status: string): string {
  switch (status) {
    case "bear":
      return "Bear Market";
    case "accumulation":
      return "Accumulazione";
    case "altseason-start":
      return "Altseason Iniziata";
    case "full-altseason":
      return "Full Altseason";
    default:
      return "Sconosciuto";
  }
}

function getStatusDescription(status: string): string {
  switch (status) {
    case "bear":
      return "Bitcoin domina il mercato";
    case "accumulation":
      return "Fase di accumulo";
    case "altseason-start":
      return "Le altcoin iniziano a performare";
    case "full-altseason":
      return "Le altcoin dominano il mercato";
    default:
      return "";
  }
}
