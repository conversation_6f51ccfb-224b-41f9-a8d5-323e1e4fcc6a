import { CMCResponse, CryptoData, EXCLUDED_SYMBOLS } from "@/types";

const CMC_API_BASE = "https://pro-api.coinmarketcap.com/v1";

export async function fetchCryptoData(): Promise<CryptoData[]> {
  const apiKey = process.env.CMC_API_KEY;

  if (!apiKey) {
    throw new Error("CMC_API_KEY non configurata nelle variabili d'ambiente");
  }

  try {
    const response = await fetch(
      `${CMC_API_BASE}/cryptocurrency/listings/latest?start=1&limit=100&convert=USD`,
      {
        headers: {
          "X-CMC_PRO_API_KEY": apiKey,
          Accept: "application/json",
        },
        // Forziamo il fetch senza cache per avere sempre dati freschi
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error(
        `Errore API CMC: ${response.status} ${response.statusText}`
      );
    }

    const data: CMCResponse = await response.json();

    if (data.status.error_code !== 0) {
      throw new Error(`Errore CMC: ${data.status.error_message}`);
    }

    return data.data;
  } catch (error) {
    console.error("Errore nel fetch dei dati crypto:", error);
    throw error;
  }
}

export function filterValidAltcoins(cryptos: CryptoData[]): CryptoData[] {
  return cryptos.filter((crypto) => {
    // Escludiamo Bitcoin
    if (crypto.symbol === "BTC") return false;

    // Escludiamo stablecoin e token backed
    if (EXCLUDED_SYMBOLS.includes(crypto.symbol)) return false;

    // Escludiamo crypto senza dati di variazione 90 giorni
    if (!crypto.quote.USD.percent_change_90d) return false;

    // Prendiamo solo le prime 50 posizioni (escludendo BTC che è sempre #1)
    if (crypto.cmc_rank > 51) return false;

    return true;
  });
}

export function getBitcoinData(cryptos: CryptoData[]): CryptoData | null {
  return cryptos.find((crypto) => crypto.symbol === "BTC") || null;
}

export function calculateAltSeasonIndex(
  altcoins: CryptoData[],
  btcChange90d: number
): { index: number; outperformingCount: number } {
  const outperformingCount = altcoins.filter(
    (altcoin) => altcoin.quote.USD.percent_change_90d > btcChange90d
  ).length;

  const index = (outperformingCount / altcoins.length) * 100;

  return { index, outperformingCount };
}

export function getMarketStatus(
  index: number
): "bear" | "accumulation" | "altseason-start" | "full-altseason" {
  if (index < 25) return "bear";
  if (index < 50) return "accumulation";
  if (index < 75) return "altseason-start";
  return "full-altseason";
}

export function getStatusColor(status: string): string {
  switch (status) {
    case "bear":
      return "text-red-500";
    case "accumulation":
      return "text-yellow-500";
    case "altseason-start":
      return "text-green-400";
    case "full-altseason":
      return "text-green-500";
    default:
      return "text-gray-500";
  }
}

export function getStatusLabel(status: string): string {
  switch (status) {
    case "bear":
      return "Bear Market";
    case "accumulation":
      return "Accumulazione";
    case "altseason-start":
      return "Altseason Iniziata";
    case "full-altseason":
      return "Full Altseason";
    default:
      return "Sconosciuto";
  }
}

// Helper functions per formato italiano
export function formatNumberIT(value: number, decimals: number = 0): string {
  if (
    value === undefined ||
    value === null ||
    isNaN(value) ||
    typeof value !== "number"
  ) {
    return "0";
  }
  return value.toLocaleString("it-IT", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
}

export function formatPercentage(value: number): string {
  if (typeof value !== "number" || isNaN(value)) return "0%";
  const formatted = formatNumberIT(value, 1);
  return `${value >= 0 ? "+" : ""}${formatted}%`;
}

export function formatPrice(price: number): string {
  if (typeof price !== "number" || isNaN(price)) return "$0.00";
  if (price < 0.01) {
    return `$${formatNumberIT(price, 6)}`;
  } else if (price < 1) {
    return `$${formatNumberIT(price, 4)}`;
  } else if (price < 100) {
    return `$${formatNumberIT(price, 2)}`;
  } else {
    return `$${formatNumberIT(price, 0)}`;
  }
}

export function formatMarketCap(marketCap: number): string {
  if (typeof marketCap !== "number" || isNaN(marketCap)) return "$0";
  if (marketCap >= 1e12) {
    return `$${formatNumberIT(marketCap / 1e12, 2)}T`;
  } else if (marketCap >= 1e9) {
    return `$${formatNumberIT(marketCap / 1e9, 2)}B`;
  } else if (marketCap >= 1e6) {
    return `$${formatNumberIT(marketCap / 1e6, 2)}M`;
  } else {
    return `$${formatNumberIT(marketCap, 0)}`;
  }
}
