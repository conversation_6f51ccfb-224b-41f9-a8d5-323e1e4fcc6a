# 🚀 AltSeason Index Tracker

Un'applicazione web minimalista e performante costruita con **Next.js v15** che monitora in tempo reale l'**Altcoin Season Index** - la percentuale delle TOP 50 criptovalute (escluse stablecoin e token backed) che stanno performando meglio di Bitcoin negli ultimi 90 giorni.

## 🎯 Caratteristiche

- **📊 Gauge Animato**: Visualizzazione dell'indice con animazioni fluide
- **📱 Mobile-First**: Design responsive ottimizzato per dispositivi mobili
- **⚡ Performance**: Cache intelligente per ottimizzare le chiamate API
- **🌙 Dark Mode**: Supporto automatico per tema scuro/chiaro
- **📈 Lista Dettagliata**: TOP 50 altcoin con performance vs Bitcoin
- **🔄 Aggiornamenti Automatici**: Dati aggiornati ogni 15 minuti

## 🛠️ Stack Tecnologico

- **Framework**: Next.js v15 (App Router)
- **Styling**: Tailwind CSS v4
- **Language**: TypeScript
- **API**: CoinMarketCap Free Tier
- **Hosting**: Vercel (ottimizzato per free tier)
- **Cache**: Sistema di cache in memoria Next.js

## 📋 Prerequisiti

- Node.js 18+
- Account CoinMarketCap (per API key gratuita)
- Account Vercel (per deploy)

## 🚀 Setup Locale

### 1. Clona il repository

```bash
git clone <repository-url>
cd altcoin-season-index
```

### 2. Installa le dipendenze

```bash
npm install
```

### 3. Configura le variabili d'ambiente

```bash
# Copia il file di esempio
cp .env.local.example .env.local

# Modifica .env.local e aggiungi la tua API key
CMC_API_KEY=your_coinmarketcap_api_key_here
```

**Come ottenere l'API Key CoinMarketCap:**

1. Vai su [https://coinmarketcap.com/api/](https://coinmarketcap.com/api/)
2. Registrati per un account gratuito
3. Copia la tua API key dal dashboard
4. Incollala nel file `.env.local`

### 4. Avvia il server di sviluppo

```bash
npm run dev
```

Apri [http://localhost:3000](http://localhost:3000) nel browser.

## 📊 Come Funziona l'Indice

L'**AltSeason Index** viene calcolato come:

```
Indice = (Numero di altcoin che battono BTC) / (Totale altcoin valide) × 100
```

### Interpretazione:

- **< 25%**: 🐻 **Bear Market** - Bitcoin domina
- **25-50%**: 🟡 **Accumulazione** - Fase di transizione
- **50-75%**: 🟢 **Altseason Iniziata** - Le altcoin performano bene
- **> 75%**: 🚀 **Full Altseason** - Le altcoin dominano il mercato

### Filtri Applicati:

- ❌ **Escluse**: Bitcoin, stablecoin (USDT, USDC, DAI, etc.)
- ❌ **Escluse**: Token backed (WBTC, stETH, etc.)
- ❌ **Escluse**: Token di exchange (LEO, CRO, OKB, etc.)
- ✅ **Incluse**: Solo le prime 50 altcoin "pure" per market cap

## 🏗️ Struttura del Progetto

```
src/
├── app/
│   ├── api/altseason/     # API route per dati
│   ├── globals.css        # Stili globali
│   ├── layout.tsx         # Layout principale
│   └── page.tsx           # Pagina home
├── components/
│   ├── AltSeasonGauge.tsx # Gauge animato
│   ├── CryptoList.tsx     # Lista crypto
│   ├── StatusIndicator.tsx # Barra di stato
│   └── LoadingSpinner.tsx # Stati di caricamento
├── lib/
│   ├── cmc.ts            # Funzioni API CoinMarketCap
│   ├── cache.ts          # Sistema di cache
│   └── utils.ts          # Utility e calcoli
└── types/
    └── index.ts          # Tipi TypeScript
```

## 🚀 Deploy su Vercel

### 1. Connetti il repository

1. Vai su [vercel.com](https://vercel.com)
2. Clicca "New Project"
3. Importa il repository GitHub

### 2. Configura le variabili d'ambiente

Nel dashboard Vercel:

- Vai su Settings → Environment Variables
- Aggiungi: `CMC_API_KEY` = `your_api_key_here`

### 3. Deploy

Il deploy avviene automaticamente ad ogni push su `main`.

### Limiti Free Tier Vercel:

- ✅ **Bandwidth**: 100GB/mese
- ✅ **Function Executions**: 100GB-hours/mese
- ✅ **API Calls**: Ottimizzate con cache (1 ogni 15 min)

## ⚡ Ottimizzazioni Performance

### Cache Strategy:

- **Durata**: 15 minuti per chiamata API
- **Tipo**: Cache in memoria (compatibile Vercel)
- **Fallback**: Re-fetch automatico se cache scaduta

### API Limits CoinMarketCap Free:

- **Limite**: 333 chiamate/giorno
- **Nostro uso**: ~96 chiamate/giorno (1 ogni 15 min)
- **Margine**: 70% di sicurezza

### Next.js Optimizations:

- Server Components per SSR
- Image optimization automatica
- Bundle splitting automatico
- Tailwind CSS purging

## 🔧 Comandi Disponibili

```bash
# Sviluppo
npm run dev

# Build produzione
npm run build

# Start produzione
npm run start

# Linting
npm run lint
```

## 🐛 Troubleshooting

### Errore "CMC_API_KEY non configurata"

- Verifica che `.env.local` esista e contenga la chiave
- Riavvia il server di sviluppo dopo aver aggiunto la chiave

### Errore "Rate limit exceeded"

- L'API gratuita ha limiti: attendi qualche minuto
- Verifica di non aver superato 333 chiamate/giorno

### Immagini crypto non si caricano

- Le immagini vengono da CoinMarketCap CDN
- Fallback automatico a iniziali del simbolo

## 📈 Roadmap Future

- [ ] Grafici storici dell'indice
- [ ] Notifiche push per cambi di stato
- [ ] Confronto con periodi precedenti
- [ ] API pubblica per l'indice
- [ ] Widget embeddabile

## 🤝 Contribuire

1. Fork del progetto
2. Crea un branch (`git checkout -b feature/AmazingFeature`)
3. Commit (`git commit -m 'Add some AmazingFeature'`)
4. Push (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📄 Licenza

Distribuito sotto licenza MIT. Vedi `LICENSE` per maggiori informazioni.

## 🙏 Riconoscimenti

- [CoinMarketCap](https://coinmarketcap.com) per i dati delle criptovalute
- [Blockchain Center](https://www.blockchaincenter.net/en/altcoin-season-index/) per l'ispirazione
- [Next.js](https://nextjs.org) e [Vercel](https://vercel.com) per l'infrastruttura
- [Tailwind CSS](https://tailwindcss.com) per lo styling

---

**⚠️ Disclaimer**: Questo strumento è solo a scopo informativo. Non costituisce consigli di investimento. Fai sempre le tue ricerche prima di investire in criptovalute.
